import styles from "./JC_Header.module.scss";
import React from "react";
import Image from "next/image";
import Link from "next/link";
import JC_HeaderButton from "./JC_HeaderButton";

export default function JC_Header() {

    return (
        <React.Fragment>

            {/* Header */}
            <div className={styles.mainContainer} id="JC_header">

                {/* Logo + Account */}
                <div className={styles.logoAccountContainer}>

                    {/* Logo */}
                    <Link href="/">
                        <Image
                            src="/logos/Main [Simple].webp"
                            alt={"MainLogo"}
                            width={0}
                            height={0}
                            className={styles.logo}
                            unoptimized
                        />
                    </Link>

                </div>

                {/* Navs */}
                <div className={styles.navsContainer}>

                    {/* Nav Buttons */}
                    <div className={styles.navButtons}>

                        <JC_HeaderButton linkToPage="customer" text="Customer" iconName="User2" iconWidth={23} />
                        <JC_HeaderButton linkToPage="property" text="Property" iconName="House" iconWidth={27} />
                        <JC_HeaderButton linkToPage="defects"  text="Defects"  iconName="Important" iconWidth={25} />
                        <JC_HeaderButton emailTo="<EMAIL>" text="Support" iconName="Email" iconWidth={23} />
                        <JC_HeaderButton linkToPage="account" text="Account" iconName="User2" iconWidth={23} />

                    </div>

                </div>

            </div>
        </React.Fragment>
    );

}