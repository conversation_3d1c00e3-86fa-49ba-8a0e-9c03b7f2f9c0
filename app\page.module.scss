@import 'global';

.mainContainer {
    padding-top: 0;
    width: 100%;
    height: 100%;
    flex-grow: 1;
    min-height: 400px;
    display: flex;
    justify-content: center;
    align-items: center;

    .contentContainer {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        gap: 30px;

        .mainLogo {
            width: 200px;
            height: auto;
        }
    }
}


// - SCREEN SIZES - //

@media (max-width: $mediumScreenSize) {
    .mainContainer {
        .contentContainer {
            .mainLogo {
                width: 160px;
            }
        }
    }
}

@media (max-width: $smallScreenSize) {
    .mainContainer {
        .contentContainer {
            .mainLogo {
                width: 140px;
            }
        }
    }
}

@media (max-width: $tinyScreenSize) {
    .mainContainer {
        .contentContainer {
            gap: 20px;

            .mainLogo {
                width: 120px;
            }
        }
    }
}

@media (max-width: $teenyTinyScreenSize) {
    .mainContainer {
        .contentContainer {
            gap: 15px;

            .mainLogo {
                width: 100px;
            }
        }
    }
}